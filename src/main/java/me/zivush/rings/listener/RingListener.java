package me.zivush.rings.listener;

import me.zivush.rings.Ring;
import me.zivush.rings.Rings;
import me.zivush.rings.manager.RingManager;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.block.Action;
import org.bukkit.event.player.PlayerInteractEvent;
import org.bukkit.event.player.PlayerJoinEvent;
import org.bukkit.event.player.PlayerQuitEvent;
import org.bukkit.inventory.ItemStack;

public class RingListener implements Listener {
    
    private final Rings plugin;
    private final RingManager ringManager;
    
    public RingListener(Rings plugin, RingManager ringManager) {
        this.plugin = plugin;
        this.ringManager = ringManager;
    }
    
    @EventHandler
    public void onPlayerJoin(PlayerJoinEvent event) {
        Player player = event.getPlayer();
        // Load player's equipped ring from database
        ringManager.loadPlayerRing(player);
    }
    
    @EventHandler
    public void onPlayerQuit(PlayerQuitEvent event) {
        Player player = event.getPlayer();
        // Unload player's ring data to free memory
        ringManager.unloadPlayerRing(player);
    }
    
    @EventHandler
    public void onPlayerInteract(PlayerInteractEvent event) {
        Player player = event.getPlayer();
        ItemStack item = event.getItem();

        // Handle attack ability for equipped rings (Shift + Left click with any item or empty hand)
        if ((event.getAction() == Action.LEFT_CLICK_AIR || event.getAction() == Action.LEFT_CLICK_BLOCK) &&
            player.isSneaking() && ringManager.hasEquippedRing(player)) {
            event.setCancelled(true);
            ringManager.executeAttack(player);
            return;
        }

        if (item == null) {
            return;
        }

        // Check if item is a ring
        Ring ring = ringManager.getRingFromItem(item);
        if (ring == null) {
            return;
        }

        // Right click to equip ring
        if (event.getAction() == Action.RIGHT_CLICK_AIR || event.getAction() == Action.RIGHT_CLICK_BLOCK) {
            event.setCancelled(true);

            // Remove item from inventory
            if (item.getAmount() > 1) {
                item.setAmount(item.getAmount() - 1);
            } else {
                player.getInventory().setItemInMainHand(null);
            }

            // Equip the ring
            ringManager.equipRing(player, ring);
        }
    }
}
