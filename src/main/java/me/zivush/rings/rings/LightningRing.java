package me.zivush.rings.rings;

import me.zivush.rings.Ring;
import me.zivush.rings.manager.MessageManager;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.potion.PotionEffect;
import org.bukkit.potion.PotionEffectType;
import org.bukkit.util.RayTraceResult;

import java.util.List;

public class LightningRing extends Ring {
    
    public LightningRing(String name, String displayName, Material material, int customModelData, 
                         List<String> lore, int attackCooldown, String ringClass) {
        super(name, displayName, material, customModelData, lore, attackCooldown, ringClass);
    }
    
    @Override
    public void applyPassiveEffects(Player player) {
        // Apply Fire Resistance effect (duration: 999999 ticks = ~13.8 hours, effectively permanent)
        player.addPotionEffect(new PotionEffect(PotionEffectType.FIRE_RESISTANCE, 999999, 0, false, false));
    }
    
    @Override
    public void removePassiveEffects(Player player) {
        // Remove Fire Resistance effect
        player.removePotionEffect(PotionEffectType.FIRE_RESISTANCE);
    }
    
    @Override
    public void executeAttack(Player player, MessageManager messageManager) {
        // Summon lightning where player is looking
        RayTraceResult rayTrace = player.rayTraceBlocks(50);
        Location targetLocation;

        if (rayTrace != null && rayTrace.getHitBlock() != null) {
            targetLocation = rayTrace.getHitBlock().getLocation().add(0, 1, 0);
        } else {
            // If no block hit, strike 10 blocks in front of player
            targetLocation = player.getLocation().add(player.getLocation().getDirection().multiply(10));
        }

        // Strike lightning at target location
        player.getWorld().strikeLightning(targetLocation);
        messageManager.sendRingAttackMessage(player, this);
    }
}
