package me.zivush.rings.rings;

import me.zivush.rings.Ring;
import me.zivush.rings.manager.MessageManager;
import org.bukkit.Location;
import org.bukkit.Material;
import org.bukkit.entity.EntityType;
import org.bukkit.entity.IronGolem;
import org.bukkit.entity.Player;
import org.bukkit.potion.PotionEffect;
import org.bukkit.potion.PotionEffectType;

import java.util.List;

public class EmeraldRing extends Ring {
    
    public EmeraldRing(String name, String displayName, Material material, int customModelData, 
                       List<String> lore, int attackCooldown, String ringClass) {
        super(name, displayName, material, customModelData, lore, attackCooldown, ringClass);
    }
    
    @Override
    public void applyPassiveEffects(Player player) {
        // Apply Hero of the Village effect (duration: 999999 ticks = ~13.8 hours, effectively permanent)
        player.addPotionEffect(new PotionEffect(PotionEffectType.HERO_OF_THE_VILLAGE, 999999, 0, false, false));
    }
    
    @Override
    public void removePassiveEffects(Player player) {
        // Remove Hero of the Village effect
        player.removePotionEffect(PotionEffectType.HERO_OF_THE_VILLAGE);
    }
    
    @Override
    public void executeAttack(Player player) {
        // Spawn a weak iron golem
        Location spawnLocation = player.getLocation().add(player.getLocation().getDirection().multiply(2));
        spawnLocation.setY(player.getLocation().getY()); // Keep same Y level
        
        // Make sure spawn location is safe (not inside blocks)
        while (spawnLocation.getBlock().getType() != Material.AIR || 
               spawnLocation.add(0, 1, 0).getBlock().getType() != Material.AIR) {
            spawnLocation.add(0, 1, 0);
        }
        
        IronGolem golem = (IronGolem) player.getWorld().spawnEntity(spawnLocation, EntityType.IRON_GOLEM);
        
        // Make golem weaker (half health)
        golem.setMaxHealth(50.0); // Default is 100
        golem.setHealth(50.0);
        
        // Make golem temporary (despawn after 5 minutes)
        golem.setRemoveWhenFarAway(true);
        
        player.sendMessage("§2You summon an iron golem to aid you!");
    }
}
