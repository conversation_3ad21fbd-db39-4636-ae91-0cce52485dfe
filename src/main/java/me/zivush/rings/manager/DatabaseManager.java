package me.zivush.rings.manager;

import me.zivush.rings.Rings;
import org.bukkit.configuration.file.FileConfiguration;
import org.bukkit.configuration.file.YamlConfiguration;

import java.io.File;
import java.io.IOException;
import java.util.UUID;

public class DatabaseManager {
    
    private final Rings plugin;
    private File databaseFile;
    private FileConfiguration databaseConfig;
    
    public DatabaseManager(Rings plugin) {
        this.plugin = plugin;
        setupDatabase();
    }
    
    private void setupDatabase() {
        databaseFile = new File(plugin.getDataFolder(), "database.yml");
        
        if (!databaseFile.exists()) {
            try {
                databaseFile.getParentFile().mkdirs();
                databaseFile.createNewFile();
            } catch (IOException e) {
                plugin.getLogger().severe("Could not create database.yml file!");
                e.printStackTrace();
            }
        }
        
        databaseConfig = YamlConfiguration.loadConfiguration(databaseFile);
    }
    
    /**
     * Save equipped ring for a player
     */
    public void saveEquippedRing(UUID playerUUID, String ringName) {
        databaseConfig.set("players." + playerUUID.toString() + ".equipped_ring", ringName);
        saveDatabase();
    }
    
    /**
     * Get equipped ring for a player
     */
    public String getEquippedRing(UUID playerUUID) {
        return databaseConfig.getString("players." + playerUUID.toString() + ".equipped_ring");
    }
    
    /**
     * Remove equipped ring for a player
     */
    public void removeEquippedRing(UUID playerUUID) {
        databaseConfig.set("players." + playerUUID.toString() + ".equipped_ring", null);
        saveDatabase();
    }
    
    /**
     * Check if player has a ring equipped
     */
    public boolean hasEquippedRing(UUID playerUUID) {
        return databaseConfig.contains("players." + playerUUID.toString() + ".equipped_ring") &&
               databaseConfig.get("players." + playerUUID.toString() + ".equipped_ring") != null;
    }
    
    /**
     * Save the database to file
     */
    private void saveDatabase() {
        try {
            databaseConfig.save(databaseFile);
        } catch (IOException e) {
            plugin.getLogger().severe("Could not save database.yml file!");
            e.printStackTrace();
        }
    }
    
    /**
     * Reload the database from file
     */
    public void reloadDatabase() {
        databaseConfig = YamlConfiguration.loadConfiguration(databaseFile);
    }
}
