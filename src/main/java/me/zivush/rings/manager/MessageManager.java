package me.zivush.rings.manager;

import me.zivush.rings.Ring;
import me.zivush.rings.Rings;
import org.bukkit.ChatColor;
import org.bukkit.entity.Player;

import java.util.HashMap;
import java.util.Map;

public class MessageManager {
    
    private final Rings plugin;
    
    public MessageManager(Rings plugin) {
        this.plugin = plugin;
    }
    
    /**
     * Send a message to a player with placeholder replacement
     */
    public void sendMessage(Player player, String messageKey, Map<String, String> placeholders) {
        String message = getMessage(messageKey, placeholders);
        if (message != null && !message.isEmpty()) {
            player.sendMessage(message);
        }
    }
    
    /**
     * Send a message to a player without placeholders
     */
    public void sendMessage(Player player, String messageKey) {
        sendMessage(player, messageKey, new HashMap<>());
    }
    
    /**
     * Get a message with placeholder replacement
     */
    public String getMessage(String messageKey, Map<String, String> placeholders) {
        String message = plugin.getConfig().getString("messages." + messageKey);
        if (message == null) {
            return null;
        }
        
        // Replace placeholders
        for (Map.Entry<String, String> entry : placeholders.entrySet()) {
            message = message.replace("%" + entry.getKey() + "%", entry.getValue());
        }
        
        return ChatColor.translateAlternateColorCodes('&', message);
    }
    
    /**
     * Get a message without placeholders
     */
    public String getMessage(String messageKey) {
        return getMessage(messageKey, new HashMap<>());
    }
    
    /**
     * Send ring equipped message
     */
    public void sendRingEquippedMessage(Player player, Ring ring) {
        Map<String, String> placeholders = new HashMap<>();
        placeholders.put("ring_name", ring.getDisplayName());
        placeholders.put("ring_class", ring.getRingClass());
        sendMessage(player, "ring_equipped", placeholders);
    }
    
    /**
     * Send ring unequipped message
     */
    public void sendRingUnequippedMessage(Player player, Ring ring) {
        Map<String, String> placeholders = new HashMap<>();
        placeholders.put("ring_name", ring.getDisplayName());
        placeholders.put("ring_class", ring.getRingClass());
        sendMessage(player, "ring_unequipped", placeholders);
    }
    
    /**
     * Send attack cooldown message
     */
    public void sendAttackCooldownMessage(Player player, Ring ring, long remainingTime) {
        Map<String, String> placeholders = new HashMap<>();
        placeholders.put("ring_name", ring.getDisplayName());
        placeholders.put("time", String.valueOf(remainingTime));
        placeholders.put("ring_class", ring.getRingClass());
        sendMessage(player, "attack_cooldown", placeholders);
    }
    
    /**
     * Send ring attack message
     */
    public void sendRingAttackMessage(Player player, Ring ring) {
        Map<String, String> placeholders = new HashMap<>();
        placeholders.put("ring_name", ring.getDisplayName());
        placeholders.put("ring_class", ring.getRingClass());
        sendMessage(player, "rings." + ring.getName() + ".attack_message", placeholders);
    }
    
    /**
     * Get action bar message
     */
    public String getActionBarMessage(Ring ring, long remainingCooldown) {
        Map<String, String> placeholders = new HashMap<>();
        placeholders.put("ring_name", ring.getDisplayName());
        placeholders.put("ring_class", ring.getRingClass());
        
        if (remainingCooldown > 0) {
            placeholders.put("cooldown", String.valueOf(remainingCooldown));
            return getMessage("action_bar.with_cooldown", placeholders);
        } else {
            return getMessage("action_bar.ready", placeholders);
        }
    }
}
