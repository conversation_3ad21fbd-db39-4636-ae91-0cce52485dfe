package me.zivush.rings.manager;

import me.zivush.rings.Ring;
import me.zivush.rings.Rings;
import org.bukkit.Sound;
import org.bukkit.entity.Player;

public class SoundManager {
    
    private final Rings plugin;
    
    public SoundManager(Rings plugin) {
        this.plugin = plugin;
    }
    
    /**
     * Play ring equip sound
     */
    public void playRingEquipSound(Player player, Ring ring) {
        playSound(player, "sounds.ring_equip");
    }
    
    /**
     * Play ring unequip sound
     */
    public void playRingUnequipSound(Player player, Ring ring) {
        playSound(player, "sounds.ring_unequip");
    }
    
    /**
     * Play ring attack sound
     */
    public void playRingAttackSound(Player player, Ring ring) {
        playSound(player, "sounds.rings." + ring.getName() + ".attack");
    }
    
    /**
     * Play cooldown sound (when attack is on cooldown)
     */
    public void playCooldownSound(Player player) {
        playSound(player, "sounds.attack_cooldown");
    }
    
    /**
     * Play a sound from config
     */
    private void playSound(Player player, String configPath) {
        if (!plugin.getConfig().getBoolean(configPath + ".enabled", true)) {
            return;
        }
        
        try {
            String soundName = plugin.getConfig().getString(configPath + ".sound", "BLOCK_NOTE_BLOCK_PLING");
            float volume = (float) plugin.getConfig().getDouble(configPath + ".volume", 1.0);
            float pitch = (float) plugin.getConfig().getDouble(configPath + ".pitch", 1.0);
            
            Sound sound = Sound.valueOf(soundName.toUpperCase());
            player.playSound(player.getLocation(), sound, volume, pitch);
            
        } catch (IllegalArgumentException e) {
            plugin.getLogger().warning("Invalid sound name in config at " + configPath + ".sound");
        } catch (Exception e) {
            plugin.getLogger().warning("Error playing sound from " + configPath + ": " + e.getMessage());
        }
    }
}
