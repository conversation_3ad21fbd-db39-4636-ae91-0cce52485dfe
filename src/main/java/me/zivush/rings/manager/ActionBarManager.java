package me.zivush.rings.manager;

import me.zivush.rings.Ring;
import me.zivush.rings.Rings;
import net.md_5.bungee.api.ChatMessageType;
import net.md_5.bungee.api.chat.TextComponent;
import org.bukkit.Bukkit;
import org.bukkit.entity.Player;
import org.bukkit.scheduler.BukkitRunnable;

public class ActionBarManager {

    private final Rings plugin;
    private final RingManager ringManager;
    private final MessageManager messageManager;
    
    public ActionBarManager(Rings plugin, RingManager ringManager, MessageManager messageManager) {
        this.plugin = plugin;
        this.ringManager = ringManager;
        this.messageManager = messageManager;
        startActionBarTask();
    }
    
    private void startActionBarTask() {
        new BukkitRunnable() {
            @Override
            public void run() {
                for (Player player : Bukkit.getOnlinePlayers()) {
                    updateActionBar(player);
                }
            }
        }.runTaskTimer(plugin, 0L, 20L); // Update every second (20 ticks)
    }
    
    private void updateActionBar(Player player) {
        if (!ringManager.hasEquippedRing(player)) {
            return; // Don't show action bar if no ring equipped
        }

        Ring equippedRing = ringManager.getEquippedRing(player);
        long remainingCooldown = ringManager.getRemainingCooldown(player);

        String actionBarMessage = messageManager.getActionBarMessage(equippedRing, remainingCooldown);

        if (actionBarMessage != null && !actionBarMessage.isEmpty()) {
            // Send action bar message
            player.spigot().sendMessage(ChatMessageType.ACTION_BAR, new TextComponent(actionBarMessage));
        }
    }
}
