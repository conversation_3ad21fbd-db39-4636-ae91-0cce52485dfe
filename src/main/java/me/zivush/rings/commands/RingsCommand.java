package me.zivush.rings.commands;

import me.zivush.rings.Ring;
import me.zivush.rings.Rings;
import me.zivush.rings.manager.MessageManager;
import me.zivush.rings.manager.RingManager;
import org.bukkit.Bukkit;
import org.bukkit.command.Command;
import org.bukkit.command.CommandExecutor;
import org.bukkit.command.CommandSender;
import org.bukkit.command.TabCompleter;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

public class RingsCommand implements CommandExecutor, TabCompleter {

    private final Rings plugin;
    private final RingManager ringManager;
    private final MessageManager messageManager;
    
    public RingsCommand(Rings plugin, RingManager ringManager, MessageManager messageManager) {
        this.plugin = plugin;
        this.ringManager = ringManager;
        this.messageManager = messageManager;
    }
    
    @Override
    public boolean onCommand(CommandSender sender, Command command, String label, String[] args) {
        if (!sender.hasPermission("rings.admin")) {
            sender.sendMessage(messageManager.getMessage("commands.no_permission"));
            return true;
        }

        if (args.length == 0) {
            sendHelpMessage(sender);
            return true;
        }

        switch (args[0].toLowerCase()) {
            case "reload":
                plugin.reloadConfig();
                sender.sendMessage(messageManager.getMessage("commands.config_reloaded"));
                break;
                
            case "give":
                if (args.length < 3) {
                    sender.sendMessage("§cUsage: /rings give <player> <ring_name>");
                    return true;
                }

                Player target = Bukkit.getPlayer(args[1]);
                if (target == null) {
                    sender.sendMessage(messageManager.getMessage("commands.player_not_found"));
                    return true;
                }

                Ring ring = ringManager.getRing(args[2]);
                if (ring == null) {
                    sender.sendMessage(messageManager.getMessage("ring_not_found") + " Available rings: " + String.join(", ", getRingNames()));
                    return true;
                }

                ItemStack ringItem = ring.createItem();
                target.getInventory().addItem(ringItem);

                Map<String, String> placeholders = new HashMap<>();
                placeholders.put("ring_name", ring.getDisplayName());
                placeholders.put("player", target.getName());

                sender.sendMessage(messageManager.getMessage("commands.ring_given", placeholders));
                messageManager.sendMessage(target, "commands.ring_received", placeholders);
                break;
                
            case "list":
                sender.sendMessage(messageManager.getMessage("commands.available_rings"));
                for (Ring r : ringManager.getAllRings()) {
                    placeholders = new HashMap<>();
                    placeholders.put("ring_name", r.getName());
                    placeholders.put("ring_display_name", r.getDisplayName());
                    sender.sendMessage(messageManager.getMessage("commands.ring_list_format", placeholders));
                }
                break;

            default:
                sendHelpMessage(sender);
                break;
        }

        return true;
    }

    private void sendHelpMessage(CommandSender sender) {
        sender.sendMessage(messageManager.getMessage("commands.help_header"));
        sender.sendMessage(messageManager.getMessage("commands.help_reload"));
        sender.sendMessage(messageManager.getMessage("commands.help_give"));
        sender.sendMessage(messageManager.getMessage("commands.help_list"));
    }
    
    private List<String> getRingNames() {
        List<String> names = new ArrayList<>();
        for (Ring ring : ringManager.getAllRings()) {
            names.add(ring.getName());
        }
        return names;
    }
    
    @Override
    public List<String> onTabComplete(CommandSender sender, Command command, String alias, String[] args) {
        if (!sender.hasPermission("rings.admin")) {
            return new ArrayList<>();
        }
        
        if (args.length == 1) {
            return Arrays.asList("reload", "give", "list");
        }
        
        if (args.length == 2 && args[0].equalsIgnoreCase("give")) {
            List<String> players = new ArrayList<>();
            for (Player player : Bukkit.getOnlinePlayers()) {
                players.add(player.getName());
            }
            return players;
        }
        
        if (args.length == 3 && args[0].equalsIgnoreCase("give")) {
            return getRingNames();
        }
        
        return new ArrayList<>();
    }
}
