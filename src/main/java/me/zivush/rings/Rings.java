package me.zivush.rings;

import me.zivush.rings.commands.RingsCommand;
import me.zivush.rings.listener.RingListener;
import me.zivush.rings.manager.ActionBarManager;
import me.zivush.rings.manager.DatabaseManager;
import me.zivush.rings.manager.MessageManager;
import me.zivush.rings.manager.RingManager;
import me.zivush.rings.manager.SoundManager;
import org.bukkit.Bukkit;
import org.bukkit.entity.Player;
import org.bukkit.plugin.java.JavaPlugin;

public final class Rings extends JavaPlugin {

    private DatabaseManager databaseManager;
    private MessageManager messageManager;
    private SoundManager soundManager;
    private RingManager ringManager;
    private ActionBarManager actionBarManager;

    @Override
    public void onEnable() {
        // Save default config
        saveDefaultConfig();

        // Initialize managers
        databaseManager = new DatabaseManager(this);
        ringManager = new RingManager(this, databaseManager);
        actionBarManager = new ActionBarManager(this, ringManager);

        // Register events
        getServer().getPluginManager().registerEvents(new RingListener(this, ringManager), this);

        // Register commands
        RingsCommand ringsCommand = new RingsCommand(this, ringManager);
        getCommand("rings").setExecutor(ringsCommand);
        getCommand("rings").setTabCompleter(ringsCommand);

        // Load rings for online players (in case of reload)
        for (Player player : Bukkit.getOnlinePlayers()) {
            ringManager.loadPlayerRing(player);
        }

        getLogger().info("Rings plugin has been enabled!");
    }

    @Override
    public void onDisable() {
        // Unload all player rings
        for (Player player : Bukkit.getOnlinePlayers()) {
            ringManager.unloadPlayerRing(player);
        }

        getLogger().info("Rings plugin has been disabled!");
    }

    public DatabaseManager getDatabaseManager() {
        return databaseManager;
    }

    public RingManager getRingManager() {
        return ringManager;
    }

    public ActionBarManager getActionBarManager() {
        return actionBarManager;
    }
}
