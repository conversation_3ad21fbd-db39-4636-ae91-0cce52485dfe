# Rings Plugin Configuration
# Configure all rings and their properties here

rings:
  speed_ring:
    display_name: "§b⚡ Speed Ring"
    material: "GOLD_INGOT"
    custom_model_data: 1001
    attack_cooldown: 30  # seconds
    class: "SPEED"
    attack_message: "§aYou leap forward with the power of speed!"
    lore:
      - "§7A ring that grants incredible speed"
      - "§7Passive: §bSpeed II"
      - "§7Attack: §bLeap Forward"
      - "§7Cooldown: §e30 seconds"
      - ""
      - "§eRight-click to equip"
      - "§eShift + Left-click to use ability"

  strength_ring:
    display_name: "§c💪 Strength Ring"
    material: "IRON_INGOT"
    custom_model_data: 1002
    attack_cooldown: 300  # seconds (5 minutes)
    class: "STRENGTH"
    attack_message: "§cYou feel an overwhelming surge of strength!"
    lore:
      - "§7A ring that enhances physical power"
      - "§7Passive: §cStrength II"
      - "§7Attack: §cStrength III for 20s"
      - "§7Cooldown: §e5 minutes"
      - ""
      - "§eRight-click to equip"
      - "§eShift + Left-click to use ability"

  lightning_ring:
    display_name: "§e⚡ Lightning Ring"
    material: "DIAMOND"
    custom_model_data: 1003
    attack_cooldown: 120  # seconds (2 minutes)
    class: "LIGHTNING"
    attack_message: "§eYou call down lightning from the heavens!"
    lore:
      - "§7A ring that commands the storms"
      - "§7Passive: §6Fire Resistance"
      - "§7Attack: §eSummon Lightning"
      - "§7Cooldown: §e2 minutes"
      - ""
      - "§eRight-click to equip"
      - "§eShift + Left-click to use ability"

  emerald_ring:
    display_name: "§a💎 Emerald Ring"
    material: "EMERALD"
    custom_model_data: 1004
    attack_cooldown: 180  # seconds (3 minutes)
    class: "EMERALD"
    attack_message: "§2You summon an iron golem to aid you!"
    lore:
      - "§7A ring blessed by villagers"
      - "§7Passive: §2Hero of the Village"
      - "§7Attack: §2Summon Iron Golem"
      - "§7Cooldown: §e3 minutes"
      - ""
      - "§eRight-click to equip"
      - "§eShift + Left-click to use ability"

# Messages sent to players
messages:
  ring_equipped: "§aYou equipped the %ring_name%§a!"
  ring_unequipped: "§cYou unequipped your %ring_name%§c!"
  attack_cooldown: "§cAttack is on cooldown for %time% seconds!"
  no_ring_equipped: "§cYou don't have a ring equipped!"
  ring_not_found: "§cRing not found!"

  # Action bar messages
  action_bar:
    with_cooldown: "§7Ring: %ring_name% §7| §cCooldown: %cooldown%s"
    ready: "§7Ring: %ring_name% §7| §aREADY"

  # Command messages
  commands:
    no_permission: "§cYou don't have permission to use this command!"
    config_reloaded: "§aRings configuration reloaded!"
    player_not_found: "§cPlayer not found!"
    ring_given: "§aGave %ring_name% §ato %player%"
    ring_received: "§aYou received %ring_name%§a!"
    available_rings: "§6Available Rings:"
    ring_list_format: "§7- §e%ring_name% §7(%ring_display_name%§7)"
    help_header: "§6=== Rings Commands ==="
    help_reload: "§e/rings reload §7- Reload configuration"
    help_give: "§e/rings give <player> <ring> §7- Give a ring to a player"
    help_list: "§e/rings list §7- List all available rings"

# Sound effects configuration
sounds:
  ring_equip:
    enabled: true
    sound: "BLOCK_ENCHANTMENT_TABLE_USE"
    volume: 1.0
    pitch: 1.2

  ring_unequip:
    enabled: true
    sound: "BLOCK_ENCHANTMENT_TABLE_USE"
    volume: 1.0
    pitch: 0.8

  attack_cooldown:
    enabled: true
    sound: "BLOCK_NOTE_BLOCK_BASS"
    volume: 0.5
    pitch: 0.5

  rings:
    speed_ring:
      attack:
        enabled: true
        sound: "ENTITY_ENDER_DRAGON_FLAP"
        volume: 0.8
        pitch: 1.5

    strength_ring:
      attack:
        enabled: true
        sound: "ENTITY_RAVAGER_ROAR"
        volume: 0.7
        pitch: 1.0

    lightning_ring:
      attack:
        enabled: true
        sound: "ENTITY_LIGHTNING_BOLT_THUNDER"
        volume: 1.0
        pitch: 1.0

    emerald_ring:
      attack:
        enabled: true
        sound: "ENTITY_IRON_GOLEM_HURT"
        volume: 0.8
        pitch: 0.8

# Ring Classes (for future expansion)
ring_classes:
  SPEED:
    name: "Speed Class"
    description: "Rings focused on movement and agility"
  STRENGTH:
    name: "Strength Class"
    description: "Rings focused on combat and power"
  LIGHTNING:
    name: "Lightning Class"
    description: "Rings focused on elemental magic"
  EMERALD:
    name: "Emerald Class"
    description: "Rings focused on utility and support"
