name: Rings
version: '${project.version}'
main: me.zivush.rings.Rings
api-version: '1.20'
author: Zivush
description: A plugin that adds magical rings with passive and active abilities
website: https://github.com/zivush/rings

commands:
  rings:
    description: Main rings command
    usage: /rings [reload|give|list]
    permission: rings.admin
    aliases: [ring]

permissions:
  rings.admin:
    description: Access to all rings commands
    default: op
  rings.use:
    description: Ability to use rings
    default: true
  rings.give:
    description: Ability to give rings to players
    default: op
